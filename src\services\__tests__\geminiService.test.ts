import { describe, it, expect, vi, beforeEach } from "vitest";
import { GeminiService } from "../geminiService";
import { GoogleGenerativeAI } from "@google/generative-ai";
import { EnhancementRequest, EnhancementMode } from "../../types";

// Mock the GoogleGenerativeAI module
vi.mock("@google/generative-ai", () => {
  const mockGenerateContent = vi.fn(() =>
    Promise.resolve({
      response: Promise.resolve({
        text: () => "Enhanced prompt text with suggestions and improvements.",
      }),
    })
  );

  const mockGetGenerativeModel = vi.fn(() => ({
    generateContent: mockGenerateContent,
  }));

  const MockGoogleGenerativeAI = vi.fn(() => ({
    getGenerativeModel: mockGetGenerativeModel,
  }));

  return { GoogleGenerativeAI: MockGoogleGenerativeAI };
});

describe("GeminiService", () => {
  let service: GeminiService;
  const MOCK_API_KEY = "test-api-key";
  const MOCK_MODEL_ID = "gemini-1.5-flash";

  beforeEach(() => {
    service = new GeminiService();
    vi.clearAllMocks();
    // Mock global fetch for listModels and validateModel
    global.fetch = vi.fn(() =>
      Promise.resolve({
        ok: true,
        json: () =>
          Promise.resolve({
            models: [
              {
                name: "models/gemini-1.5-flash",
                supportedGenerationMethods: ["generateContent"],
              },
              {
                name: "models/gemini-pro",
                supportedGenerationMethods: ["generateContent"],
              },
              {
                name: "models/embedding-001",
                supportedGenerationMethods: ["embedContent"],
              },
            ],
          }),
      })
    ) as any;
  });

  it("should initialize the service with an API key and model", () => {
    service.initialize({
      apiKey: MOCK_API_KEY,
      model: MOCK_MODEL_ID,
      temperature: 0.5,
    });
    expect(GoogleGenerativeAI).toHaveBeenCalledWith(MOCK_API_KEY);
    expect(service.isInitialized()).toBe(true);
  });

  it("should get the generative model with correct configuration", () => {
    service.initialize({
      apiKey: MOCK_API_KEY,
      model: MOCK_MODEL_ID,
      temperature: 0.7,
    });
    const mockGenAIInstance = (GoogleGenerativeAI as any).mock.results[0].value;
    expect(mockGenAIInstance.getGenerativeModel).toHaveBeenCalledWith({
      model: MOCK_MODEL_ID,
      generationConfig: { temperature: 0.7 },
    });
  });

  describe("listModels", () => {
    it("should throw an error if API key is not set", async () => {
      const uninitializedService = new GeminiService();
      await expect(uninitializedService.listModels()).rejects.toThrow(
        "API key not set. Please initialize the service first."
      );
    });

    it("should fetch and filter supported models", async () => {
      service.initialize({ apiKey: MOCK_API_KEY });
      const result = await service.listModels();
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining(`key=${MOCK_API_KEY}`)
      );
      expect(result.models).toEqual([
        {
          name: "models/gemini-1.5-flash",
          supportedGenerationMethods: ["generateContent"],
        },
        {
          name: "models/gemini-pro",
          supportedGenerationMethods: ["generateContent"],
        },
      ]);
    });

    it("should handle API errors when listing models", async () => {
      service.initialize({ apiKey: MOCK_API_KEY });
      global.fetch = vi.fn(() =>
        Promise.resolve({ ok: false, status: 400 })
      ) as any;
      await expect(service.listModels()).rejects.toThrow(
        "Failed to fetch models: HTTP error! status: 400"
      );
    });
  });

  describe("enhancePrompt", () => {
    beforeEach(() => {
      service.initialize({ apiKey: MOCK_API_KEY, model: MOCK_MODEL_ID });
    });

    it("should throw an error if service is not initialized", async () => {
      const uninitializedService = new GeminiService();
      await expect(
        uninitializedService.enhancePrompt({
          originalPrompt: "test",
          mode: "quick",
        })
      ).rejects.toThrow(
        "Gemini service not initialized. Please set your API key."
      );
    });

    it("should call generateContent with correct prompts", async () => {
      const request: EnhancementRequest = {
        originalPrompt: "Hello",
        mode: "quick" as EnhancementMode,
        style: "concise",
        context: "greeting",
      };
      await service.enhancePrompt(request);

      const mockGenAIInstance = (GoogleGenerativeAI as any).mock.results[0]
        .value;
      const mockGetGenerativeModelInstance =
        mockGenAIInstance.getGenerativeModel.mock.results[0].value;
      expect(
        mockGetGenerativeModelInstance.generateContent
      ).toHaveBeenCalledWith([
        expect.objectContaining({
          text: expect.stringContaining("expert prompt engineer"),
        }),
        expect.objectContaining({ text: expect.stringContaining("Hello") }),
      ]);
    });

    it("should return enhanced prompt, suggestions, and improvements", async () => {
      const request: EnhancementRequest = {
        originalPrompt: "Hello",
        mode: "quick" as EnhancementMode,
      };
      const response = await service.enhancePrompt(request);
      expect(response.enhancedPrompt).toBe(
        "Enhanced prompt text with suggestions and improvements."
      );
      expect(response.suggestions).toEqual([]);
      expect(response.improvements).toEqual([]);
    });

    it("should handle errors during prompt enhancement", async () => {
      const mockGenAIInstance = (GoogleGenerativeAI as any).mock.results[0]
        .value;
      const mockGetGenerativeModelInstance =
        mockGenAIInstance.getGenerativeModel.mock.results[0].value;
      mockGetGenerativeModelInstance.generateContent.mockImplementationOnce(
        () => Promise.reject(new Error("API call failed"))
      );

      const request: EnhancementRequest = {
        originalPrompt: "Hello",
        mode: "quick" as EnhancementMode,
      };
      await expect(service.enhancePrompt(request)).rejects.toThrow(
        "Failed to enhance prompt: API call failed"
      );
    });

    it("should handle 429 errors with specific message during prompt enhancement", async () => {
      const mockGenAIInstance = (GoogleGenerativeAI as any).mock.results[0]
        .value;
      const mockGetGenerativeModelInstance =
        mockGenAIInstance.getGenerativeModel.mock.results[0].value;
      mockGetGenerativeModelInstance.generateContent.mockImplementationOnce(
        () =>
          Promise.reject(new Error("Error 429: Quota exceeded for this model"))
      );

      const request: EnhancementRequest = {
        originalPrompt: "Hello",
        mode: "quick" as EnhancementMode,
      };
      await expect(service.enhancePrompt(request)).rejects.toThrow(
        "Selected model doesn't have a free quota tier. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits."
      );
    });

    it("should handle other 429 errors with generic message", async () => {
      const mockGenAIInstance = (GoogleGenerativeAI as any).mock.results[0]
        .value;
      const mockGetGenerativeModelInstance =
        mockGenAIInstance.getGenerativeModel.mock.results[0].value;
      mockGetGenerativeModelInstance.generateContent.mockImplementationOnce(
        () => Promise.reject(new Error("Some other 429 error"))
      );

      const request: EnhancementRequest = {
        originalPrompt: "Hello",
        mode: "quick" as EnhancementMode,
      };
      await expect(service.enhancePrompt(request)).rejects.toThrow(
        "Failed to enhance prompt: Some other 429 error"
      );
    });
  });

  describe("buildSystemPrompt", () => {
    it("should build a quick mode system prompt", () => {
      const prompt = (service as any).buildSystemPrompt(
        "quick" as EnhancementMode
      );
      expect(prompt).toContain("Focus on quick improvements");
      expect(prompt).toContain(
        "Return only the enhanced prompt without additional commentary or explanations."
      );
    });

    it("should build a structured mode system prompt", () => {
      const prompt = (service as any).buildSystemPrompt(
        "structured" as EnhancementMode
      );
      expect(prompt).toContain(
        "Transform the prompt into a well-structured format"
      );
      expect(prompt).toContain("**ROLE:**");
      expect(prompt).toContain(
        "Format your response using the structured sections outlined above."
      );
    });

    it("should build a template mode system prompt", () => {
      const prompt = (service as any).buildSystemPrompt(
        "template" as EnhancementMode
      );
      expect(prompt).toContain("Create a reusable template structure");
      expect(prompt).toContain("Placeholder variables in [BRACKETS]");
      expect(prompt).toContain(
        "Format your response as a reusable template with clear placeholders and instructions for customization."
      );
    });

    it("should build a batch mode system prompt", () => {
      const prompt = (service as any).buildSystemPrompt(
        "batch" as EnhancementMode
      );
      expect(prompt).toContain("Process this as part of a batch operation");
      expect(prompt).toContain(
        "Return only the enhanced prompt without additional commentary or explanations."
      );
    });

    it("should include style instructions when provided", () => {
      const prompt = (service as any).buildSystemPrompt(
        "quick" as EnhancementMode,
        "creative"
      );
      expect(prompt).toContain(
        "Emphasize creativity, exploration, and open-ended thinking."
      );
    });

    it("should not include style instructions if not provided", () => {
      const prompt = (service as any).buildSystemPrompt(
        "quick" as EnhancementMode
      );
      expect(prompt).not.toContain("Emphasize creativity"); // Check for a specific style instruction
    });
  });

  describe("buildUserPrompt", () => {
    it("should build a user prompt with only original prompt", () => {
      const request: EnhancementRequest = {
        originalPrompt: "My test prompt",
        mode: "quick" as EnhancementMode,
      };
      const prompt = (service as any).buildUserPrompt(request);
      expect(prompt).toBe('Please enhance this prompt:\n\n"My test prompt"');
    });

    it("should build a user prompt with original prompt and context", () => {
      const request: EnhancementRequest = {
        originalPrompt: "My test prompt",
        mode: "quick" as EnhancementMode,
        context: "additional info",
      };
      const prompt = (service as any).buildUserPrompt(request);
      expect(prompt).toBe(
        'Please enhance this prompt:\n\n"My test prompt"\n\nAdditional context: additional info'
      );
    });
  });

  describe("extractSuggestions", () => {
    it("should extract suggestions based on keywords", () => {
      const enhancedPrompt =
        "This is an enhanced prompt. Consider adding more details. Specify the format. Provide context.";
      const suggestions = (service as any).extractSuggestions(enhancedPrompt);
      expect(suggestions).toEqual([
        "Consider adding more specific examples",
        "Specify the desired output format",
        "Provide additional context for better results",
      ]);
    });

    it("should return empty array if no keywords found", () => {
      const enhancedPrompt = "A simple prompt.";
      const suggestions = (service as any).extractSuggestions(enhancedPrompt);
      expect(suggestions).toEqual([]);
    });
  });

  describe("extractImprovements", () => {
    it("should extract improvements based on length, colon, and example", () => {
      const enhancedPrompt =
        "This is a very long enhanced prompt with: sections and an example. It's over one hundred characters long, so it should trigger the length check.";
      const improvements = (service as any).extractImprovements(enhancedPrompt);
      expect(improvements).toEqual([
        "Added more detailed instructions",
        "Improved structure with clear sections",
        "Included examples for clarity",
      ]);
    });

    it("should return empty array if no improvements detected", () => {
      const enhancedPrompt = "Short prompt.";
      const improvements = (service as any).extractImprovements(enhancedPrompt);
      expect(improvements).toEqual([]);
    });
  });

  describe("validateModel", () => {
    it("should return true for a valid model", async () => {
      service.initialize({ apiKey: MOCK_API_KEY });
      global.fetch = vi.fn(() => Promise.resolve({ ok: true })) as any;
      const isValid = await service.validateModel("gemini-pro");
      expect(isValid).toBe(true);
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining("models/gemini-pro")
      );
    });

    it("should return false for an invalid model", async () => {
      service.initialize({ apiKey: MOCK_API_KEY });
      global.fetch = vi.fn(() => Promise.resolve({ ok: false })) as any;
      const isValid = await service.validateModel("invalid-model");
      expect(isValid).toBe(false);
    });

    it("should return false if API key is not set", async () => {
      const uninitializedService = new GeminiService();
      const isValid = await uninitializedService.validateModel("gemini-pro");
      expect(isValid).toBe(false);
    });

    it("should return false on fetch error", async () => {
      service.initialize({ apiKey: MOCK_API_KEY });
      global.fetch = vi.fn(() =>
        Promise.reject(new Error("Network error"))
      ) as any;
      const isValid = await service.validateModel("gemini-pro");
      expect(isValid).toBe(false);
    });
  });

  describe("updateModel", () => {
    it("should update the generative model", () => {
      service.initialize({ apiKey: MOCK_API_KEY, model: "old-model" });
      const mockGenAIInstance = (GoogleGenerativeAI as any).mock.results[0]
        .value;

      service.updateModel("new-model");
      expect(mockGenAIInstance.getGenerativeModel).toHaveBeenCalledWith({
        model: "new-model",
        generationConfig: { temperature: 0.3 },
      });
    });

    it("should throw an error if service is not initialized", () => {
      const uninitializedService = new GeminiService();
      expect(() => uninitializedService.updateModel("new-model")).toThrow(
        "Service not initialized. Please set API key first."
      );
    });
  });

  describe("isInitialized", () => {
    it("should return true if service is initialized", () => {
      service.initialize({ apiKey: MOCK_API_KEY, model: MOCK_MODEL_ID });
      expect(service.isInitialized()).toBe(true);
    });

    it("should return false if service is not initialized", () => {
      const uninitializedService = new GeminiService();
      expect(uninitializedService.isInitialized()).toBe(false);
    });
  });
});
