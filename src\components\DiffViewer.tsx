import { FC } from "react";
import ReactDiffViewer, { DiffMethod } from "react-diff-viewer-continued";

interface DiffViewerProps {
  originalText: string;
  enhancedText: string;
  title?: string;
  className?: string;
}

export const DiffViewer: FC<DiffViewerProps> = ({
  originalText,
  enhancedText,
  title = "Prompt Comparison",
  className = "",
}) => {
  return (
    <div
      className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}
    >
      <div className="px-4 py-3 border-b border-gray-200 flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        <div className="flex items-center space-x-4 text-sm text-gray-600">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-red-200 rounded"></div>
            <span>Removed</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-200 rounded"></div>
            <span>Added</span>
          </div>
        </div>
      </div>
      <div className="overflow-auto max-h-96">
        <ReactDiffViewer
          oldValue={originalText}
          newValue={enhancedText}
          splitView={true}
          compareMethod={DiffMethod.WORDS}
          leftTitle="Original Prompt"
          rightTitle="Enhanced Prompt"
          hideLineNumbers={false}
          useDarkTheme={false}
        />
      </div>
    </div>
  );
};
