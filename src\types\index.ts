// Electron API types
export interface ElectronAPI {
  // API key management
  storeApiKey: (apiKey: string) => Promise<{ success: boolean }>;
  getApiKey: () => Promise<{ apiKey: string | null }>;

  // Model selection management
  storeSelectedModel: (modelId: string) => Promise<{ success: boolean }>;
  getSelectedModel: () => Promise<{ modelId: string | null }>;

  // File operations
  showSaveDialog: () => Promise<{ canceled: boolean; filePath?: string }>;
  showOpenDialog: () => Promise<{ canceled: boolean; filePaths?: string[] }>;

  // Clipboard operations
  clipboardWriteText: (
    text: string
  ) => Promise<{ success: boolean; error?: string }>;
  clipboardReadText: () => Promise<{
    success: boolean;
    text?: string;
    error?: string;
  }>;

  // System notifications
  showNotification: (options: {
    title: string;
    body: string;
    silent?: boolean;
  }) => Promise<{ success: boolean; error?: string }>;

  // External links
  openExternal: (url: string) => Promise<{ success: boolean; error?: string }>;

  // Window management
  minimizeToTray: () => Promise<{ success: boolean; error?: string }>;
  showWindow: () => Promise<{ success: boolean }>;

  // Event listeners
  onQuickEnhanceClipboard: (callback: (text: string) => void) => void;
  onOpenSettings: (callback: () => void) => void;

  // Platform info
  platform: string;
  isDev: () => Promise<boolean>;
}

// Gemini API types
export interface GeminiConfig {
  apiKey: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
}

export interface EnhancementRequest {
  originalPrompt: string;
  mode: EnhancementMode;
  context?: string;
  style?: EnhancementStyle;
}

export interface EnhancementResponse {
  enhancedPrompt: string;
  suggestions?: string[];
  improvements?: string[];
}

// Model types for Gemini API
export interface GeminiModel {
  name: string;
  baseModelId: string;
  version: string;
  displayName: string;
  description: string;
  inputTokenLimit: number;
  outputTokenLimit: number;
  supportedGenerationMethods: string[];
  temperature?: number;
  maxTemperature?: number;
  topP?: number;
  topK?: number;
}

export interface ListModelsResponse {
  models: GeminiModel[];
  nextPageToken?: string;
}

// Enhancement modes
export type EnhancementMode = "quick" | "structured" | "template" | "batch";

export type EnhancementStyle =
  | "creative"
  | "concise"
  | "technical"
  | "detailed";

// Structured prompt sections
export interface StructuredPromptSections {
  role: string;
  context: string;
  instructions: string;
  goal: string;
  constraints: string;
  examples?: string;
  outputFormat?: string;
  [key: string]: string | undefined; // Allow custom sections
}

// Section configuration for structured prompts
export interface SectionConfig {
  key: string;
  label: string;
  placeholder: string;
  isRequired: boolean;
  isCustom: boolean;
  canDelete: boolean;
  canRename: boolean;
}

// Template types
export interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  sections: StructuredPromptSections;
  placeholders: Record<string, string>;
  createdAt: Date;
  updatedAt: Date;
}

export interface TemplateCategory {
  id: string;
  name: string;
  description: string;
  templates: PromptTemplate[];
}

// Application state types
export interface AppState {
  originalPrompt: string;
  enhancedPrompt: string;
  isLoading: boolean;
  error: string | null;
  apiKey: string | null;
  currentMode: EnhancementMode;
  currentStyle: EnhancementStyle;
  structuredSections: StructuredPromptSections;
  selectedTemplate: PromptTemplate | null;
  customTemplates: PromptTemplate[];
  customSections: SectionConfig[];
  layout: LayoutState;
  batchProcessing: BatchProcessingState;
  history: PromptHistory[];
}

// History types
export interface PromptHistory {
  id: string;
  timestamp: Date;
  originalPrompt: string;
  enhancedPrompt: string;
  mode: EnhancementMode;
  style: EnhancementStyle;
  category?: string;
  tags?: string[];
  rating?: number; // 1-5 stars
  notes?: string;
}

// Batch processing types
export interface BatchItem {
  id: string;
  originalPrompt: string;
  enhancedPrompt?: string;
  status: "pending" | "processing" | "completed" | "error";
  error?: string;
}

export interface BatchProcessingState {
  items: BatchItem[];
  isProcessing: boolean;
  currentIndex: number;
  totalItems: number;
  completedItems: number;
  failedItems: number;
}

// Quality checklist types
export interface QualityCheckItem {
  id: string;
  label: string;
  description: string;
  isRequired: boolean;
  status: "unchecked" | "checked" | "warning" | "error";
  validator?: (prompt: string, enhancedPrompt?: string) => boolean;
}

export interface QualityChecklist {
  mode: EnhancementMode;
  items: QualityCheckItem[];
}

// Diff view types
export interface DiffChange {
  type: "added" | "removed" | "modified" | "unchanged";
  content: string;
  lineNumber?: number;
}

// Layout types
export interface LayoutState {
  showRightSidebar: boolean;
  rightSidebarTab: "quality" | "history" | "batch";
  showDiffView: boolean;
}

// Global window interface extension
declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
