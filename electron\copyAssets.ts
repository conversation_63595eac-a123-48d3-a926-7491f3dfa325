import * as fs from "fs";
import * as path from "path";
import { fileURLToPath } from "url";
import { dirname } from "path";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Create dist/electron/assets directory if it doesn't exist
const targetDir = path.join(__dirname, "../dist/electron/assets");
if (!fs.existsSync(targetDir)) {
  fs.mkdirSync(targetDir, { recursive: true });
}

// Copy icon.ico from assets to dist/electron/assets
const sourceIcon = path.join(__dirname, "../assets/icon.ico");
const targetIcon = path.join(targetDir, "icon.ico");

try {
  fs.copyFileSync(sourceIcon, targetIcon);
  console.log("✅ Assets copied successfully");
} catch (err) {
  console.error("❌ Error copying assets:", err);
  process.exit(1);
}
