import { GoogleGenerativeA<PERSON> } from "@google/generative-ai";
import {
  EnhancementRequest,
  EnhancementResponse,
  GeminiConfig,
  ListModelsResponse,
} from "../types";

export class GeminiService {
  private genAI: GoogleGenerativeAI | null = null;
  private model: any = null;
  private apiKey: string | null = null;

  initialize(config: GeminiConfig) {
    this.apiKey = config.apiKey;
    this.genAI = new GoogleGenerativeAI(config.apiKey);

    // Use baseModelId format for API calls - use the most stable model
    const modelId = config.model || "gemini-1.5-flash";

    this.model = this.genAI.getGenerativeModel({
      model: modelId,
      generationConfig: {
        temperature: config.temperature || 0.3,
      },
    });
  }

  async listModels(): Promise<ListModelsResponse> {
    if (!this.apiKey) {
      throw new Error("API key not set. Please initialize the service first.");
    }

    try {
      const response = await fetch(
        `https://generativelanguage.googleapis.com/v1beta/models?key=${this.apiKey}`
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: ListModelsResponse = await response.json();

      // Filter models that support generateContent
      const supportedModels = data.models.filter((model) => {
        return model.supportedGenerationMethods.includes("generateContent");
      });

      return {
        models: supportedModels,
        nextPageToken: data.nextPageToken,
      };
    } catch (error) {
      console.error("Error fetching models:", error);
      throw new Error(
        `Failed to fetch models: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  async enhancePrompt(
    request: EnhancementRequest
  ): Promise<EnhancementResponse> {
    if (!this.model) {
      throw new Error(
        "Gemini service not initialized. Please set your API key."
      );
    }

    const systemPrompt = this.buildSystemPrompt(request.mode, request.style);
    const userPrompt = this.buildUserPrompt(request);

    try {
      const result = await this.model.generateContent([
        { text: systemPrompt },
        { text: userPrompt },
      ]);

      const response = await result.response;
      const enhancedPrompt = response.text();

      return {
        enhancedPrompt: enhancedPrompt.trim(),
        suggestions: this.extractSuggestions(enhancedPrompt),
        improvements: this.extractImprovements(enhancedPrompt),
      };
    } catch (error) {
      console.error("Error calling Gemini API:", error);

      // Check if it's a 429 error related to quota/billing (common patterns for pro models)
      if (error instanceof Error && error.message.includes("429")) {
        // Check for specific quota-related keywords that indicate pro model limitations
        if (
          error.message.toLowerCase().includes("quota") ||
          error.message.toLowerCase().includes("billing") ||
          error.message.toLowerCase().includes("free tier") ||
          error.message.toLowerCase().includes("payment")
        ) {
          throw new Error(
            "Selected model doesn't have a free quota tier. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits."
          );
        }
      }

      throw new Error(
        `Failed to enhance prompt: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  private buildSystemPrompt(mode: string, style?: string): string {
    const basePrompt = `You are an expert prompt engineer. Your task is to enhance and improve prompts to make them more effective, clear, and likely to produce better results from AI systems.`;

    const modeInstructions = {
      quick: `Focus on quick improvements: clarity, specificity, and structure. Make the prompt more actionable and precise. Enhance the existing prompt while maintaining its original intent and format.`,

      structured: `Transform the prompt into a well-structured format with clear sections. Use this structure:

**ROLE:** Define the AI's role and expertise
**CONTEXT:** Provide relevant background information
**INSTRUCTIONS:** Clear, step-by-step directions
**GOAL:** Specific desired outcome
**CONSTRAINTS:** Limitations, requirements, or guidelines
**EXAMPLES:** (if helpful) Sample inputs/outputs
**OUTPUT FORMAT:** Specify the desired response format

Ensure each section is comprehensive and well-defined. If the original prompt lacks information for a section, intelligently infer what would be most helpful.`,

      template: `Create a reusable template structure that can be adapted for similar use cases. Include:
- Clear section headers
- Placeholder variables in [BRACKETS] for customizable elements
- Flexible structure that can be modified for different scenarios
- Instructions for how to use the template
- Example values for placeholders

Make it a professional template that others can easily customize and reuse.`,

      batch: `Process this as part of a batch operation. Focus on consistency and standardization across multiple prompts. Ensure the enhanced version follows a consistent format and style that would work well when processing multiple similar prompts.`,
    };

    const styleInstructions = style
      ? {
          creative: `Emphasize creativity, exploration, and open-ended thinking. Encourage innovative approaches and out-of-the-box solutions. Use engaging language that inspires creative responses.`,
          concise: `Keep the enhanced prompt concise and to-the-point while maintaining effectiveness. Remove unnecessary words but ensure all essential information is preserved. Aim for clarity and brevity.`,
          technical: `Focus on technical accuracy, precision, and detailed specifications. Use appropriate technical language and terminology. Include specific parameters, requirements, and measurable criteria.`,
          detailed: `Provide comprehensive, detailed instructions with examples and clear expectations. Include thorough explanations, multiple examples, and step-by-step guidance. Leave no ambiguity about what is expected.`,
        }[style]
      : "";

    const outputInstruction =
      mode === "structured"
        ? `\n\nFormat your response using the structured sections outlined above. Each section should be clearly labeled and contain relevant, detailed content.`
        : mode === "template"
        ? `\n\nFormat your response as a reusable template with clear placeholders and instructions for customization.`
        : `\n\nReturn only the enhanced prompt without additional commentary or explanations.`;

    return `${basePrompt}\n\n${
      modeInstructions[mode as keyof typeof modeInstructions]
    }\n\n${styleInstructions}${outputInstruction}`;
  }

  private buildUserPrompt(request: EnhancementRequest): string {
    let prompt = `Please enhance this prompt:\n\n"${request.originalPrompt}"`;

    if (request.context) {
      prompt += `\n\nAdditional context: ${request.context}`;
    }

    return prompt;
  }

  private extractSuggestions(enhancedPrompt: string): string[] {
    // Simple extraction logic - in a real implementation, you might use more sophisticated parsing
    const suggestions: string[] = [];

    if (enhancedPrompt.includes("consider")) {
      suggestions.push("Consider adding more specific examples");
    }
    if (enhancedPrompt.includes("format")) {
      suggestions.push("Specify the desired output format");
    }
    if (enhancedPrompt.includes("context")) {
      suggestions.push("Provide additional context for better results");
    }

    return suggestions;
  }

  private extractImprovements(enhancedPrompt: string): string[] {
    // Simple improvement detection
    const improvements: string[] = [];

    if (enhancedPrompt.length > 100) {
      improvements.push("Added more detailed instructions");
    }
    if (enhancedPrompt.includes(":")) {
      improvements.push("Improved structure with clear sections");
    }
    if (enhancedPrompt.includes("example")) {
      improvements.push("Included examples for clarity");
    }

    return improvements;
  }

  async validateModel(modelId: string): Promise<boolean> {
    if (!this.apiKey) {
      return false;
    }

    try {
      // Try to get model info to validate it exists
      const response = await fetch(
        `https://generativelanguage.googleapis.com/v1beta/models/${modelId}?key=${this.apiKey}`
      );
      return response.ok;
    } catch (error) {
      console.error("Model validation failed:", error);
      return false;
    }
  }

  updateModel(modelId: string) {
    if (!this.genAI) {
      throw new Error("Service not initialized. Please set API key first.");
    }

    this.model = this.genAI.getGenerativeModel({
      model: modelId,
      generationConfig: {
        temperature: 0.3,
      },
    });
  }

  isInitialized(): boolean {
    return this.model !== null;
  }
}

export const geminiService = new GeminiService();
