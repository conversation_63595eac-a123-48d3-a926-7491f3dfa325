import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { ErrorHandlingService } from "../errorHandlingService";

describe("ErrorHandlingService", () => {
  let service: ErrorHandlingService;

  beforeEach(() => {
    service = new ErrorHandlingService();
    vi.useFakeTimers();
    // Mock console methods to prevent logging during tests
    vi.spyOn(console, "log").mockImplementation(() => {});
    vi.spyOn(console, "warn").mockImplementation(() => {});
    vi.spyOn(console, "error").mockImplementation(() => {});
    vi.spyOn(console, "info").mockImplementation(() => {});
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.restoreAllMocks();
    service.clearErrorLog();
  });

  describe("withRetry", () => {
    it("should return the result on the first successful attempt", async () => {
      const operation = vi.fn().mockResolvedValue("Success");
      const result = await service.withRetry(operation);
      expect(result).toBe("Success");
      expect(operation).toHaveBeenCalledTimes(1);
    });

    it("should retry the operation on failure and succeed eventually", async () => {
      const operation = vi
        .fn()
        .mockRejectedValueOnce(new Error("Network error")) // retryable
        .mockRejectedValueOnce(new Error("API error 503")) // retryable
        .mockResolvedValue("Success");

      const promise = service.withRetry(operation, { baseDelay: 10 });

      await vi.runAllTimersAsync();

      await expect(promise).resolves.toBe("Success");
      expect(operation).toHaveBeenCalledTimes(3);
    });

    it("should not retry for non-retryable errors", async () => {
      const operation = vi
        .fn()
        .mockRejectedValue(new Error("Validation error"));
      let caughtError: Error | null = null;

      try {
        await service.withRetry(operation, { maxRetries: 2, baseDelay: 10 });
      } catch (e) {
        caughtError = e as Error;
      }

      expect(caughtError).toBeInstanceOf(Error);
      expect(caughtError?.message).toBe("Validation error");
      expect(operation).toHaveBeenCalledTimes(1);
    });
  });

  describe("categorizeError", () => {
    it("should categorize network errors correctly", () => {
      const error = new Error("fetch failed due to network issue");
      const categorized = service.categorizeError(error);
      expect(categorized.type).toBe("network");
      expect(categorized.retryable).toBe(true);
    });
    it("should categorize API errors (retryable and non-retryable)", () => {
      const error503 = new Error("API error 503 Service Unavailable");
      const categorized503 = service.categorizeError(error503);
      expect(categorized503.type).toBe("api");
      expect(categorized503.retryable).toBe(true);

      const error401 = new Error("API error 401 Unauthorized");
      const categorized401 = service.categorizeError(error401);
      expect(categorized401.type).toBe("api");
      expect(categorized401.retryable).toBe(false);
    });

    it("should categorize validation errors", () => {
      const error = new Error("Invalid input provided");
      const categorized = service.categorizeError(error);
      expect(categorized.type).toBe("validation");
      expect(categorized.retryable).toBe(false);
    });

    it("should categorize system errors", () => {
      const error = new Error("File access denied");
      const categorized = service.categorizeError(error);
      expect(categorized.type).toBe("system");
      expect(categorized.retryable).toBe(false);
    });
  });

  describe("getUserFriendlyMessage", () => {
    it("should return a message for network errors", () => {
      const error = new Error("Network connection issue");
      const message = service.getUserFriendlyMessage(error);
      expect(message).toBe(
        "Network connection issue. Please check your internet connection and try again."
      );
    });

    it("should return a message for authentication errors", () => {
      const error = new Error("API 401 Error");
      const message = service.getUserFriendlyMessage(error);
      expect(message).toBe("Authentication failed. Please check your API key.");
    });

    it("should return a generic message for other API errors", () => {
      const error = new Error("API 500 server error");
      const message = service.getUserFriendlyMessage(error);
      expect(message).toBe(
        "API service is temporarily unavailable. Please try again later."
      );
    });
  });

  describe("createCircuitBreaker", () => {
    it("should be in a closed state initially and allow calls", async () => {
      const operation = vi.fn().mockResolvedValue("Success");
      const breaker = service.createCircuitBreaker(operation);
      const result = await breaker();
      expect(result).toBe("Success");
      expect(operation).toHaveBeenCalledTimes(1);
    });

    it("should open the circuit after reaching the failure threshold", async () => {
      const operation = vi.fn().mockRejectedValue(new Error("Failure"));
      const breaker = service.createCircuitBreaker(operation, {
        failureThreshold: 2,
        resetTimeout: 1000,
        monitoringPeriod: 2000,
      });

      await expect(breaker()).rejects.toThrow("Failure");
      await expect(breaker()).rejects.toThrow("Failure");

      // Circuit should now be open
      await expect(breaker()).rejects.toThrow("Circuit breaker is open");
      expect(operation).toHaveBeenCalledTimes(2);
    });

    it("should transition to half-open after the reset timeout and close on success", async () => {
      const operation = vi
        .fn()
        .mockRejectedValueOnce(new Error("Failure"))
        .mockResolvedValue("Success");

      const breaker = service.createCircuitBreaker(operation, {
        failureThreshold: 1,
        resetTimeout: 500,
        monitoringPeriod: 1000,
      });

      // Trip the breaker
      await expect(breaker()).rejects.toThrow("Failure");
      await expect(breaker()).rejects.toThrow("Circuit breaker is open");

      // Wait for reset timeout
      await vi.advanceTimersByTimeAsync(600);

      // Should be half-open, next call should succeed and close the breaker
      const result = await breaker();
      expect(result).toBe("Success");
      expect(operation).toHaveBeenCalledTimes(2);

      // Should be closed again
      await breaker();
      expect(operation).toHaveBeenCalledTimes(3);
    });

    it("should re-open if the half-open call fails", async () => {
      const operation = vi.fn().mockRejectedValue(new Error("Failure"));
      const breaker = service.createCircuitBreaker(operation, {
        failureThreshold: 1,
        resetTimeout: 500,
        monitoringPeriod: 1000,
      });

      // Trip the breaker
      await expect(breaker()).rejects.toThrow("Failure");
      await expect(breaker()).rejects.toThrow("Circuit breaker is open");

      // Wait for reset timeout
      await vi.advanceTimersByTimeAsync(600);

      // Should be half-open, call will fail and re-open the breaker
      await expect(breaker()).rejects.toThrow("Failure");

      // Should be open again
      await expect(breaker()).rejects.toThrow("Circuit breaker is open");
      expect(operation).toHaveBeenCalledTimes(2);
    });
  });

  describe("logError and getErrorStats", () => {
    it("should log errors and provide statistics", () => {
      service.logError(new Error("Network error"));
      service.logError(new Error("API 403 error"));
      service.logError(new Error("Another Network error"));

      const stats = service.getErrorStats();
      expect(stats.total).toBe(3);
      expect(stats.byType).toEqual({ network: 2, api: 1 });
      expect(stats.bySeverity).toEqual({ medium: 2, high: 1 });
      expect(stats.recentErrors).toHaveLength(3);
    });

    it("should respect the max log size", () => {
      for (let i = 0; i < 110; i++) {
        service.logError(new Error(`Error ${i}`), { type: "unknown" });
      }
      const stats = service.getErrorStats();
      expect(stats.total).toBe(100);
      expect(stats.recentErrors[9].originalError.message).toBe("Error 109");
    });

    it("clearErrorLog should clear all logged errors", () => {
      service.logError(new Error("Some error"));
      service.clearErrorLog();
      const stats = service.getErrorStats();
      expect(stats.total).toBe(0);
    });
  });
});
